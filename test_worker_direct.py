#!/usr/bin/env python3
"""
Direct test of subprocess worker to verify uv environment.
"""

import json
import subprocess
import tempfile
import time
import shutil
import sys
from pathlib import Path


def test_worker_direct():
    """Test the subprocess worker directly."""
    print("Testing subprocess worker directly...")

    # Check if uv is available
    if not shutil.which("uv"):
        print("FAIL uv is not available")
        return False

    # Create test task data
    task_data = {
        "goal": "测试任务",
        "device_serial": "test_device",
        "job_id": f"test-{int(time.time())}",
        "max_steps": 5,
        "timeout": 60,
        "max_retries": 1,
        "debug": True
    }

    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as input_file:
        json.dump(task_data, input_file, indent=2)
        input_file_path = input_file.name

    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as output_file:
        output_file_path = output_file.name

    try:
        print(f"Input file: {input_file_path}")
        print(f"Output file: {output_file_path}")
        print(f"Task data: {task_data}")

        # Test with uv
        print("\nTesting with uv environment...")
        cmd = ["uv", "run", "python", "subprocess_worker.py", input_file_path, output_file_path]
        print(f"Command: {' '.join(cmd)}")

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2 minutes timeout
        )

        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        if result.stderr:
            print(f"Stderr: {result.stderr}")

        # Check if output file was created
        if Path(output_file_path).exists():
            print("OK Output file created")
            with open(output_file_path, 'r') as f:
                output_data = json.load(f)
            print(f"Output data: {json.dumps(output_data, indent=2)}")

            if output_data.get("success") is not None:
                print("OK Worker executed successfully")
                return True
            else:
                print("FAIL Worker output format incorrect")
                return False
        else:
            print("FAIL Output file not created")
            return False

    except subprocess.TimeoutExpired:
        print("FAIL Worker timed out")
        return False
    except Exception as e:
        print(f"FAIL Error running worker: {e}")
        return False
    finally:
        # Clean up
        try:
            Path(input_file_path).unlink(missing_ok=True)
            Path(output_file_path).unlink(missing_ok=True)
        except:
            pass


def test_imports():
    """Test if required imports work in uv environment."""
    print("\nTesting imports in uv environment...")

    test_script = '''
import sys
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")

try:
    import asyncio
    print("OK asyncio imported")
except ImportError as e:
    print(f"FAIL asyncio import failed: {e}")

try:
    from llama_index.llms.deepseek import DeepSeek
    print("OK DeepSeek imported")
except ImportError as e:
    print(f"FAIL DeepSeek import failed: {e}")

try:
    from droidrun.agent.droid import DroidAgent
    print("OK DroidAgent imported")
except ImportError as e:
    print(f"FAIL DroidAgent import failed: {e}")

try:
    from config import config
    print("OK config imported")
    print(f"LLM Model: {config.LLM_MODEL}")
except ImportError as e:
    print(f"FAIL config import failed: {e}")
'''

    try:
        result = subprocess.run(
            ["uv", "run", "python", "-c", test_script],
            capture_output=True,
            text=True,
            timeout=30
        )

        print("Import test output:")
        print(result.stdout)
        if result.stderr:
            print("Import test errors:")
            print(result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"FAIL Import test failed: {e}")
        return False


def main():
    """Main test function."""
    print("Direct Subprocess Worker Test")
    print("=" * 50)

    # Test imports first
    import_success = test_imports()

    # Test worker execution
    worker_success = test_worker_direct()

    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Import Test: {'PASS' if import_success else 'FAIL'}")
    print(f"Worker Test: {'PASS' if worker_success else 'FAIL'}")

    if import_success and worker_success:
        print("\nALL TESTS PASSED!")
        print("OK UV environment is working correctly")
        print("OK Subprocess worker can execute tasks")
        print("OK All dependencies are available")
    else:
        print("\nSome tests failed!")
        if not import_success:
            print("- Import test failed: Check uv environment and dependencies")
        if not worker_success:
            print("- Worker test failed: Check subprocess_worker.py")


if __name__ == "__main__":
    main()
