"""
Test script for uv-based subprocess execution.
"""

import asyncio
import httpx
import time
import subprocess
import shutil


BASE_URL = "http://localhost:43826"


def check_uv_available():
    """Check if uv is available in the system."""
    return shutil.which("uv") is not None


def check_uv_project():
    """Check if we're in a uv project."""
    try:
        result = subprocess.run(["uv", "run", "python", "--version"], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except:
        return False


async def test_uv_subprocess_execution():
    """Test subprocess execution with uv environment."""
    print("🧪 Testing UV-based Subprocess Execution")
    print("="*50)
    
    # Check uv availability
    if not check_uv_available():
        print("❌ uv is not available in the system")
        print("Please install uv: https://docs.astral.sh/uv/getting-started/installation/")
        return False
    
    print("✅ uv is available")
    
    # Check if we're in a uv project
    if not check_uv_project():
        print("❌ Not in a uv project or uv environment not working")
        print("Please run 'uv sync' to set up the environment")
        return False
    
    print("✅ uv project environment is working")
    
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "test_app",
            "nonce": "test_nonce"
        },
        "arg": {
            "site": "test-site",
            "eacId": "test-eac",
            "jobId": f"job-uv-test-{int(time.time())}",
            "eventType": "Test",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Submit task
        print("📤 Submitting task for uv subprocess execution...")
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request
        )
        
        if response.status_code != 200:
            print(f"❌ Task submission failed: {response.status_code}")
            return False
            
        result = response.json()
        if result.get("code") != 0:
            print(f"❌ Task submission failed: {result.get('msg')}")
            return False
            
        job_id = test_request["arg"]["jobId"]
        print(f"✅ Task submitted: {job_id}")
        
        # Wait for subprocess to start
        print("⏳ Waiting for uv subprocess to start...")
        await asyncio.sleep(5)
        
        # Check active subprocesses
        debug_response = await client.get(f"{BASE_URL}/debug/active-tasks")
        if debug_response.status_code == 200:
            debug_result = debug_response.json()
            subprocess_count = debug_result.get("active_subprocess_count", 0)
            subprocesses = debug_result.get("active_subprocesses", {})
            
            print(f"📊 Active subprocesses: {subprocess_count}")
            if job_id in subprocesses:
                subprocess_info = subprocesses[job_id]
                pid = subprocess_info.get("pid")
                running = subprocess_info.get("running")
                print(f"✅ UV subprocess found - PID: {pid}, Running: {running}")
            else:
                print("⚠️  Subprocess not found in active list")
        
        # Monitor task execution
        print("👀 Monitoring task execution...")
        for i in range(30):  # Monitor for up to 30 seconds
            await asyncio.sleep(1)
            
            status_response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get("code") == 0:
                    data = status_result.get("data", {})
                    state = data.get("state")
                    msg = data.get("resultMsg", "")
                    
                    print(f"📊 Task state: {state}")
                    if msg:
                        print(f"📝 Message: {msg}")
                    
                    if state in ["completed", "failed", "cancelled"]:
                        if state == "completed":
                            print("✅ Task completed successfully in uv environment!")
                            return True
                        else:
                            print(f"❌ Task finished with state: {state}")
                            print(f"Message: {msg}")
                            return False
                    elif state == "running":
                        if i == 10:  # After 10 seconds, test cancellation
                            print("🛑 Testing cancellation...")
                            cancel_response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
                            if cancel_response.status_code == 200:
                                cancel_result = cancel_response.json()
                                if cancel_result.get("code") == 0:
                                    print("✅ Cancel request accepted")
                                else:
                                    print(f"❌ Cancel failed: {cancel_result.get('msg')}")
        
        print("⏰ Task monitoring timed out")
        return False


async def test_environment_isolation():
    """Test that subprocess runs in isolated uv environment."""
    print("\n🔍 Testing Environment Isolation")
    print("="*50)
    
    # This test verifies that the subprocess uses the uv environment
    # by checking if it can import packages that should be available
    
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "test_app",
            "nonce": "test_nonce"
        },
        "arg": {
            "site": "test-site",
            "eacId": "test-eac",
            "jobId": f"job-env-test-{int(time.time())}",
            "eventType": "Test",
            "content": "简单测试任务",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("📤 Submitting task to test environment isolation...")
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                job_id = test_request["arg"]["jobId"]
                print(f"✅ Task submitted: {job_id}")
                
                # Wait a bit for execution to start
                await asyncio.sleep(8)
                
                # Check if subprocess is running with correct environment
                debug_response = await client.get(f"{BASE_URL}/debug/active-tasks")
                if debug_response.status_code == 200:
                    debug_result = debug_response.json()
                    subprocesses = debug_result.get("active_subprocesses", {})
                    
                    if job_id in subprocesses:
                        print("✅ Subprocess is running in isolated environment")
                        
                        # Cancel the test task
                        await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
                        return True
                    else:
                        print("❌ Subprocess not found")
                        return False
        
        print("❌ Environment isolation test failed")
        return False


async def main():
    """Main test function."""
    print("🚀 Testing UV-based Subprocess Execution")
    print("This tests that DroidRun tasks run in the correct uv Python environment")
    print()
    
    # Check service health
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Service is healthy")
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    # Run tests
    test1_result = await test_uv_subprocess_execution()
    test2_result = await test_environment_isolation()
    
    # Summary
    print("\n" + "="*60)
    print("UV SUBPROCESS TEST SUMMARY")
    print("="*60)
    print(f"UV Subprocess Execution: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Environment Isolation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL UV SUBPROCESS TESTS PASSED!")
        print("✅ Tasks run in correct uv Python environment")
        print("✅ Environment isolation working properly")
        print("✅ No dependency conflicts")
        print("\n💡 UV subprocess approach is working correctly!")
    else:
        print("\n❌ Some tests failed!")
        print("Please check:")
        print("1. uv is installed and available")
        print("2. uv project is properly set up (run 'uv sync')")
        print("3. All dependencies are installed in uv environment")


if __name__ == "__main__":
    asyncio.run(main())
