"""
Focused test script for task cancellation functionality.
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any


BASE_URL = "http://localhost:43826"


async def submit_long_running_task():
    """Submit a task that should take a while to complete."""
    print("Submitting a long-running task...")
    
    # Use a more complex task that should take longer
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "wbb2dej3",
            "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
        },
        "arg": {
            "site": "eac-android-any",
            "eacId": "eac-0112",
            "jobId": f"job-cancel-test-{int(time.time())}",
            "eventType": "Autonomous",
            "content": "打开设置应用，然后打开关于手机，查看安卓版本号，然后打开开发者选项，最后返回主屏幕",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Task submission response: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("✅ Long-running task submitted successfully!")
                return test_request["arg"]["jobId"]
            else:
                print(f"❌ Task submission failed: {result.get('msg')}")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None


async def get_task_status(job_id: str):
    """Get task status."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                task_data = result.get("data", {})
                state = task_data.get("state")
                print(f"Task {job_id} state: {state}")
                return state, task_data
            else:
                print(f"❌ Error getting task status: {result.get('msg')}")
                return None, None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None, None


async def cancel_task(job_id: str):
    """Cancel a task."""
    print(f"Cancelling task {job_id}...")
    
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
        print(f"Cancel response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Cancel result: {result}")
            
            if result.get("code") == 0:
                print("✅ Cancel request accepted!")
                return True
            else:
                print(f"❌ Cancel request failed: {result.get('msg')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False


async def wait_for_task_to_start(job_id: str, max_wait: int = 30):
    """Wait for task to start running."""
    print(f"Waiting for task {job_id} to start...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        state, _ = await get_task_status(job_id)
        
        if state == "running":
            print(f"✅ Task {job_id} is now running!")
            return True
        elif state in ["completed", "failed", "cancelled"]:
            print(f"❌ Task {job_id} finished too quickly with state: {state}")
            return False
        elif state == "pending":
            print(f"Task {job_id} is still pending, waiting...")
            await asyncio.sleep(2)
        else:
            print(f"Unknown state: {state}")
            await asyncio.sleep(2)
    
    print(f"❌ Task did not start within {max_wait} seconds")
    return False


async def verify_cancellation(job_id: str, max_wait: int = 30):
    """Verify that task was actually cancelled."""
    print(f"Verifying cancellation of task {job_id}...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        state, task_data = await get_task_status(job_id)
        
        if state == "cancelled":
            print(f"✅ Task {job_id} was successfully cancelled!")
            print(f"Result message: {task_data.get('resultMsg', 'N/A')}")
            return True
        elif state in ["completed", "failed"]:
            print(f"❌ Task {job_id} finished with state '{state}' instead of being cancelled")
            print(f"Result message: {task_data.get('resultMsg', 'N/A')}")
            return False
        elif state in ["pending", "running"]:
            print(f"Task {job_id} is still {state}, waiting for cancellation...")
            await asyncio.sleep(2)
        else:
            print(f"Unknown state: {state}")
            await asyncio.sleep(2)
    
    print(f"❌ Cancellation verification timed out after {max_wait} seconds")
    return False


async def test_immediate_cancellation():
    """Test cancelling a task immediately after submission."""
    print("\n" + "="*60)
    print("TEST: Immediate Cancellation")
    print("="*60)
    
    # Submit task
    job_id = await submit_long_running_task()
    if not job_id:
        print("❌ Failed to submit task")
        return False
    
    # Cancel immediately
    print("\nCancelling task immediately...")
    cancel_success = await cancel_task(job_id)
    
    if cancel_success:
        # Verify cancellation
        verification_success = await verify_cancellation(job_id)
        return verification_success
    else:
        return False


async def test_running_task_cancellation():
    """Test cancelling a task while it's running."""
    print("\n" + "="*60)
    print("TEST: Running Task Cancellation")
    print("="*60)
    
    # Submit task
    job_id = await submit_long_running_task()
    if not job_id:
        print("❌ Failed to submit task")
        return False
    
    # Wait for task to start
    if not await wait_for_task_to_start(job_id):
        print("❌ Task did not start running")
        return False
    
    # Let it run for a bit
    print("Letting task run for 10 seconds...")
    await asyncio.sleep(10)
    
    # Cancel the running task
    cancel_success = await cancel_task(job_id)
    
    if cancel_success:
        # Verify cancellation
        verification_success = await verify_cancellation(job_id)
        return verification_success
    else:
        return False


async def main():
    """Main test function."""
    print("🧪 Starting Task Cancellation Tests...")
    
    # Test health first
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code != 200:
                print("❌ Service is not healthy!")
                return
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    print("✅ Service is healthy, starting tests...")
    
    # Run tests
    test1_result = await test_immediate_cancellation()
    test2_result = await test_running_task_cancellation()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Immediate Cancellation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Running Task Cancellation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All cancellation tests PASSED!")
    else:
        print("\n❌ Some cancellation tests FAILED!")


if __name__ == "__main__":
    asyncio.run(main())
