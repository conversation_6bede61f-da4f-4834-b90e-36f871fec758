"""
Simple test script to verify task execution and cancellation.
"""

import asyncio
import httpx
import time


BASE_URL = "http://localhost:43826"


async def submit_task():
    """Submit a simple task."""
    print("Submitting task...")
    
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "test_app",
            "nonce": "test_nonce"
        },
        "arg": {
            "site": "test-site",
            "eacId": "test-eac",
            "jobId": f"job-simple-{int(time.time())}",
            "eventType": "Test",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request
        )
        
        print(f"Submit response: {response.status_code}")
        result = response.json()
        print(f"Result: {result}")
        
        if response.status_code == 200 and result.get("code") == 0:
            return test_request["arg"]["jobId"]
        return None


async def get_status(job_id: str):
    """Get task status."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                data = result.get("data", {})
                state = data.get("state")
                msg = data.get("resultMsg", "")
                print(f"Task {job_id}: {state} - {msg}")
                return state
        return None


async def cancel_task(job_id: str):
    """Cancel a task."""
    print(f"Cancelling task {job_id}...")
    
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
        
        print(f"Cancel response: {response.status_code}")
        result = response.json()
        print(f"Cancel result: {result}")
        
        return response.status_code == 200 and result.get("code") == 0


async def test_basic_flow():
    """Test basic task flow."""
    print("=== Testing Basic Task Flow ===")
    
    # Submit task
    job_id = await submit_task()
    if not job_id:
        print("❌ Failed to submit task")
        return
    
    print(f"✅ Task submitted: {job_id}")
    
    # Wait a bit and check status
    await asyncio.sleep(3)
    state = await get_status(job_id)
    
    if state in ["pending", "running"]:
        print(f"✅ Task is {state}")
        
        # Try to cancel
        cancel_success = await cancel_task(job_id)
        if cancel_success:
            print("✅ Cancel request accepted")
            
            # Check if actually cancelled
            await asyncio.sleep(2)
            final_state = await get_status(job_id)
            
            if final_state == "cancelled":
                print("✅ Task successfully cancelled!")
            else:
                print(f"❌ Task state is {final_state}, not cancelled")
        else:
            print("❌ Cancel request failed")
    else:
        print(f"Task already finished with state: {state}")


async def main():
    """Main test function."""
    print("🧪 Starting Simple Tests...")
    
    # Check health
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Service is healthy")
            else:
                print("❌ Service health check failed")
                return
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    await test_basic_flow()
    print("\n🎉 Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
