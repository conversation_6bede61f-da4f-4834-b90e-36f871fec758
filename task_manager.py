"""
Async task manager for handling droidrun tasks.
"""

import asyncio
import time
import logging
import httpx
from typing import Dict, Optional, Any
from models import TaskInfo, TaskState, CallbackRequest, CallbackArgModel, AuthModel
from config import config

logger = logging.getLogger(__name__)


class AsyncTaskManager:
    """Manages async task execution and lifecycle."""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start the task manager and cleanup task."""
        logger.info("Starting AsyncTaskManager...")
        self._cleanup_task = asyncio.create_task(self._cleanup_old_tasks())
        
    async def stop(self):
        """Stop the task manager and cleanup."""
        logger.info("Stopping AsyncTaskManager...")
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        # Cancel all running tasks
        for task_info in self.tasks.values():
            if task_info.task_handle and not task_info.task_handle.done():
                task_info.task_handle.cancel()
                
    def create_task(self, job_id: str, eac_id: str, event_type: str, 
                   content: str, device: str, auth_info: AuthModel,
                   callback_url: Optional[str] = None) -> TaskInfo:
        """Create a new task."""
        if job_id in self.tasks:
            raise ValueError(f"Task with job_id {job_id} already exists")
            
        task_info = TaskInfo(
            job_id=job_id,
            eac_id=eac_id,
            event_type=event_type,
            content=content,
            device=device,
            state=TaskState.PENDING,
            created_at=time.time(),
            callback_url=callback_url,
            auth_info=auth_info
        )
        
        self.tasks[job_id] = task_info
        logger.info(f"Created task {job_id}: {content}")
        return task_info
        
    def get_task(self, job_id: str) -> Optional[TaskInfo]:
        """Get task by job_id."""
        return self.tasks.get(job_id)
        
    def update_task_state(self, job_id: str, state: TaskState, 
                         result_msg: Optional[str] = None):
        """Update task state."""
        if job_id not in self.tasks:
            return False
            
        task_info = self.tasks[job_id]
        task_info.state = state
        if result_msg:
            task_info.result_msg = result_msg
            
        if state == TaskState.RUNNING and not task_info.started_at:
            task_info.started_at = time.time()
        elif state in [TaskState.COMPLETED, TaskState.FAILED, TaskState.CANCELLED]:
            task_info.completed_at = time.time()
            
        logger.info(f"Task {job_id} state updated to {state}")
        return True
        
    async def execute_task(self, job_id: str, task_executor_func):
        """Execute a task asynchronously."""
        task_info = self.get_task(job_id)
        if not task_info:
            logger.error(f"Task {job_id} not found")
            return
            
        try:
            # Update state to running
            self.update_task_state(job_id, TaskState.RUNNING)
            
            # Execute the task
            result = await task_executor_func(task_info)
            
            # Update state based on result
            if result and result.get("success", False):
                self.update_task_state(job_id, TaskState.COMPLETED, 
                                     result.get("reason", "Task completed successfully"))
                # Send success callback
                await self._send_callback(task_info, state=1, 
                                        msg=result.get("reason", "Task completed successfully"))
            else:
                error_msg = result.get("reason", "Task failed") if result else "Task failed"
                self.update_task_state(job_id, TaskState.FAILED, error_msg)
                # Send failure callback
                await self._send_callback(task_info, state=3, msg=error_msg)
                
        except asyncio.CancelledError:
            self.update_task_state(job_id, TaskState.CANCELLED, "Task was cancelled")
            await self._send_callback(task_info, state=3, msg="Task was cancelled")
            logger.info(f"Task {job_id} was cancelled")
        except Exception as e:
            error_msg = f"Task execution error: {str(e)}"
            self.update_task_state(job_id, TaskState.FAILED, error_msg)
            await self._send_callback(task_info, state=3, msg=error_msg)
            logger.error(f"Task {job_id} failed: {e}")
            
    def start_task(self, job_id: str, task_executor_func) -> bool:
        """Start task execution in background."""
        task_info = self.get_task(job_id)
        if not task_info:
            return False
            
        # Create and store the asyncio task
        task_handle = asyncio.create_task(self.execute_task(job_id, task_executor_func))
        task_info.task_handle = task_handle
        
        logger.info(f"Started task {job_id} in background")
        return True
        
    def cancel_task(self, job_id: str) -> bool:
        """Cancel a running task."""
        task_info = self.get_task(job_id)
        if not task_info:
            return False
            
        if task_info.task_handle and not task_info.task_handle.done():
            task_info.task_handle.cancel()
            logger.info(f"Cancelled task {job_id}")
            return True
            
        return False
        
    async def _send_callback(self, task_info: TaskInfo, state: int, msg: str):
        """Send callback to external endpoint."""
        if not task_info.callback_url or not task_info.auth_info:
            logger.debug(f"No callback URL or auth info for task {task_info.job_id}")
            return
            
        callback_data = CallbackRequest(
            v="1.0.0",
            auth=task_info.auth_info,
            arg=CallbackArgModel(
                eacId=task_info.eac_id,
                jobId=task_info.job_id,
                eventType=task_info.event_type,
                state=state,
                msg=msg
            )
        )
        
        for attempt in range(config.CALLBACK_RETRIES):
            try:
                async with httpx.AsyncClient(timeout=config.CALLBACK_TIMEOUT) as client:
                    response = await client.post(
                        task_info.callback_url,
                        json=callback_data.model_dump(),
                        headers={"Content-Type": "application/json"}
                    )
                    
                    if response.status_code == 200:
                        logger.info(f"Callback sent successfully for task {task_info.job_id}")
                        return
                    else:
                        logger.warning(f"Callback failed with status {response.status_code} for task {task_info.job_id}")
                        
            except Exception as e:
                logger.error(f"Callback attempt {attempt + 1} failed for task {task_info.job_id}: {e}")
                
            if attempt < config.CALLBACK_RETRIES - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
    async def _cleanup_old_tasks(self):
        """Periodically cleanup old completed tasks."""
        while True:
            try:
                await asyncio.sleep(config.TASK_CLEANUP_INTERVAL)
                current_time = time.time()
                
                tasks_to_remove = []
                for job_id, task_info in self.tasks.items():
                    if (task_info.state in [TaskState.COMPLETED, TaskState.FAILED, TaskState.CANCELLED] and
                        current_time - task_info.created_at > config.TASK_MAX_AGE):
                        tasks_to_remove.append(job_id)
                        
                for job_id in tasks_to_remove:
                    del self.tasks[job_id]
                    logger.info(f"Cleaned up old task {job_id}")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in task cleanup: {e}")


# Global task manager instance
task_manager = AsyncTaskManager()
