import asyncio
import nest_asyncio
import logging

logger = logging.getLogger(__name__)
nest_asyncio_applied = False


def async_to_sync(func):
    """
    Convert an async function to a sync function.

    Args:
        func: Async function to convert

    Returns:
        Callable: Synchronous version of the async function
    """

    def wrapper(*args, **kwargs):
        global nest_asyncio_applied
        coro = func(*args, **kwargs)

        try:
            # Try to get the running event loop.
            loop = asyncio.get_running_loop()

            # Check if we're in uvloop which doesn't support nest_asyncio
            loop_type = type(loop).__name__
            if 'uvloop' in loop_type.lower():
                logger.warning(f"uvloop detected ({loop_type}), cannot use nest_asyncio - running async function directly")
                # For uvloop, we can't use nest_asyncio
                # Instead, we'll raise an exception to indicate this function should be called differently
                raise RuntimeError(
                    f"Cannot convert async function to sync in uvloop environment. "
                    f"Function {func.__name__} should be called with 'await' instead."
                )
            else:
                # For standard asyncio loops, try to use nest_asyncio
                if nest_asyncio and not nest_asyncio_applied:
                    try:
                        nest_asyncio.apply()
                        nest_asyncio_applied = True
                    except ValueError as e:
                        if "Can't patch loop" in str(e):
                            logger.warning(f"Cannot patch loop type {loop_type}, falling back to task creation")
                            # Fallback to task creation
                            task = asyncio.create_task(coro)
                            import time
                            while not task.done():
                                time.sleep(0.001)
                            return task.result()
                        else:
                            raise

                # Run the coroutine to completion within the running loop.
                return loop.run_until_complete(coro)

        except RuntimeError:
            # No running event loop found.
            try:
                # Check if there's a loop policy and a current event loop set, even if not running.
                loop = asyncio.get_event_loop_policy().get_event_loop()
                if loop.is_running():
                    # This case should ideally be caught by get_running_loop(),
                    # but as a fallback, handle similarly if loop is running.
                    loop_type = type(loop).__name__
                    if 'uvloop' in loop_type.lower():
                        logger.warning(f"uvloop detected ({loop_type}), cannot use nest_asyncio")
                        raise RuntimeError(
                            f"Cannot convert async function to sync in uvloop environment. "
                            f"Function {func.__name__} should be called with 'await' instead."
                        )
                    else:
                        if nest_asyncio and not nest_asyncio_applied:
                            try:
                                nest_asyncio.apply()
                                nest_asyncio_applied = True
                            except ValueError:
                                pass  # Ignore patch errors
                        return loop.run_until_complete(coro)
                else:
                    # Loop exists but is not running, run until complete.
                    return loop.run_until_complete(coro)
            except RuntimeError:
                # If get_event_loop() also fails (no loop set at all for this thread),
                # use asyncio.run() which creates a new loop.
                return asyncio.run(coro)

    return wrapper