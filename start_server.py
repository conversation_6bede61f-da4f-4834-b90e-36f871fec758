#!/usr/bin/env python3
"""
Simple script to start the FastAPI droidrun service.
"""

import os
import uvicorn
from config import config

# Force disable uvloop to avoid compatibility issues with nest_asyncio
os.environ["UVLOOP_DISABLE"] = "1"

if __name__ == "__main__":
    print("🚀 Starting FastAPI droidrun service...")
    print(f"Host: {config.HOST}")
    print(f"Port: {config.PORT}")
    print(f"Debug: {config.DEBUG}")
    print(f"LLM Provider: {config.LLM_PROVIDER}")
    print(f"LLM Model: {config.LLM_MODEL}")
    print("-" * 50)

    uvicorn.run(
        "app:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG,
        log_level=config.LOG_LEVEL.lower(),
        loop="asyncio"  # Force use of standard asyncio instead of uvloop
    )
