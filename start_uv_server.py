#!/usr/bin/env python3
"""
Start the FastAPI droidrun service using uv environment.
"""

import os
import subprocess
import sys
import shutil
from config import config


def check_uv_environment():
    """Check if uv is available and project is set up."""
    print("🔍 Checking uv environment...")
    
    # Check if uv is installed
    if not shutil.which("uv"):
        print("❌ uv is not installed")
        print("Please install uv: https://docs.astral.sh/uv/getting-started/installation/")
        return False
    
    print("✅ uv is installed")
    
    # Check if we're in a uv project
    try:
        result = subprocess.run(["uv", "run", "python", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            python_version = result.stdout.strip()
            print(f"✅ uv environment is working: {python_version}")
            return True
        else:
            print("❌ uv environment is not working")
            print("Please run 'uv sync' to set up the environment")
            return False
    except Exception as e:
        print(f"❌ Error checking uv environment: {e}")
        return False


def start_server_with_uv():
    """Start the server using uv environment."""
    print("🚀 Starting FastAPI droidrun service with uv...")
    print(f"Host: {config.HOST}")
    print(f"Port: {config.PORT}")
    print(f"Debug: {config.DEBUG}")
    print(f"LLM Provider: {config.LLM_PROVIDER}")
    print(f"LLM Model: {config.LLM_MODEL}")
    print("-" * 50)
    
    # Use uv to run uvicorn
    cmd = [
        "uv", "run", "uvicorn", "app:app",
        "--host", config.HOST,
        "--port", str(config.PORT),
        "--loop", "asyncio"
    ]
    
    if config.DEBUG:
        cmd.append("--reload")
    
    print(f"Command: {' '.join(cmd)}")
    print("Starting server...")
    
    try:
        # Run the server
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


def main():
    """Main function."""
    print("🔧 DroidRun FastAPI Service with UV Environment")
    print("=" * 50)
    
    # Check uv environment
    if not check_uv_environment():
        print("\n💡 To fix this:")
        print("1. Install uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
        print("2. Set up the project: uv sync")
        print("3. Run this script again")
        sys.exit(1)
    
    # Check if subprocess worker exists
    if not os.path.exists("subprocess_worker.py"):
        print("❌ subprocess_worker.py not found")
        print("Please ensure all files are in the current directory")
        sys.exit(1)
    
    print("✅ All checks passed")
    
    # Start the server
    start_server_with_uv()


if __name__ == "__main__":
    main()
