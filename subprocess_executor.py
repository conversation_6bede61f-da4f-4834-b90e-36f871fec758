"""
Subprocess-based task executor for DroidRun tasks.
This avoids LlamaIndex instrumentation callback issues by running each task in a separate process.
"""

import asyncio
import json
import logging
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class SubprocessTaskExecutor:
    """Execute DroidRun tasks in separate subprocesses."""
    
    def __init__(self):
        self.active_processes: Dict[str, subprocess.Popen] = {}
        
    async def execute_task(self, task_info) -> Dict[str, Any]:
        """Execute a task in a subprocess."""
        job_id = task_info.job_id
        
        try:
            # Create temporary files for communication
            input_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            output_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            
            # Prepare task data
            task_data = {
                "goal": task_info.content,
                "device_serial": task_info.device,
                "job_id": job_id,
                "max_steps": 15,
                "timeout": 1000,
                "max_retries": 3,
                "debug": False
            }
            
            # Write task data to input file
            json.dump(task_data, input_file)
            input_file.close()
            
            output_file.close()
            
            # Start subprocess
            cmd = [
                "python", 
                "subprocess_worker.py",
                input_file.name,
                output_file.name
            ]
            
            logger.info(f"Starting subprocess for task {job_id}")
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Store process reference
            self.active_processes[job_id] = process
            
            # Wait for process to complete
            while process.poll() is None:
                # Check if task was cancelled
                from task_manager import task_manager
                current_task = task_manager.get_task(job_id)
                if current_task and current_task.state.value == "cancelled":
                    logger.info(f"Terminating subprocess for cancelled task {job_id}")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Force killing subprocess for task {job_id}")
                        process.kill()
                        process.wait()
                    
                    # Clean up
                    self.active_processes.pop(job_id, None)
                    Path(input_file.name).unlink(missing_ok=True)
                    Path(output_file.name).unlink(missing_ok=True)
                    
                    raise asyncio.CancelledError("Task was cancelled")
                
                # Sleep briefly to avoid busy waiting
                await asyncio.sleep(0.5)
            
            # Process completed, read result
            return_code = process.returncode
            stdout, stderr = process.communicate()
            
            # Clean up process reference
            self.active_processes.pop(job_id, None)
            
            if return_code == 0:
                # Read result from output file
                try:
                    with open(output_file.name, 'r') as f:
                        result = json.load(f)
                    logger.info(f"Subprocess task {job_id} completed successfully")
                    return result
                except Exception as e:
                    logger.error(f"Failed to read result for task {job_id}: {e}")
                    return {
                        "success": False,
                        "reason": f"Failed to read result: {str(e)}"
                    }
            else:
                # Process failed
                logger.error(f"Subprocess task {job_id} failed with code {return_code}")
                logger.error(f"Stderr: {stderr}")
                return {
                    "success": False,
                    "reason": f"Subprocess failed with code {return_code}: {stderr}"
                }
                
        except asyncio.CancelledError:
            # Handle cancellation
            if job_id in self.active_processes:
                process = self.active_processes[job_id]
                logger.info(f"Cancelling subprocess for task {job_id}")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing subprocess for task {job_id}")
                    process.kill()
                    process.wait()
                self.active_processes.pop(job_id, None)
            raise
            
        except Exception as e:
            logger.error(f"Error executing subprocess task {job_id}: {e}")
            # Clean up process if it exists
            if job_id in self.active_processes:
                process = self.active_processes[job_id]
                process.terminate()
                self.active_processes.pop(job_id, None)
            return {
                "success": False,
                "reason": f"Subprocess execution error: {str(e)}"
            }
            
        finally:
            # Clean up temporary files
            try:
                Path(input_file.name).unlink(missing_ok=True)
                Path(output_file.name).unlink(missing_ok=True)
            except:
                pass
    
    def cancel_task(self, job_id: str) -> bool:
        """Cancel a running task by terminating its subprocess."""
        if job_id in self.active_processes:
            process = self.active_processes[job_id]
            logger.info(f"Terminating subprocess for task {job_id}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning(f"Force killing subprocess for task {job_id}")
                process.kill()
                process.wait()
            
            self.active_processes.pop(job_id, None)
            return True
        return False
    
    def get_active_tasks(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active subprocess tasks."""
        active_info = {}
        for job_id, process in self.active_processes.items():
            active_info[job_id] = {
                "pid": process.pid,
                "running": process.poll() is None,
                "return_code": process.returncode
            }
        return active_info
    
    def cleanup(self):
        """Clean up all active processes."""
        for job_id, process in list(self.active_processes.items()):
            logger.info(f"Cleaning up subprocess for task {job_id}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        self.active_processes.clear()


# Global subprocess executor instance
subprocess_executor = SubprocessTaskExecutor()
