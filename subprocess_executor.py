"""
Subprocess-based task executor for DroidRun tasks.
This avoids LlamaIndex instrumentation callback issues by running each task in a separate process.
"""

import asyncio
import json
import logging
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class SubprocessTaskExecutor:
    """Execute DroidRun tasks in separate subprocesses."""

    def __init__(self):
        self.active_processes: Dict[str, subprocess.Popen] = {}

    async def execute_task(self, task_info) -> Dict[str, Any]:
        """Execute a task in a subprocess."""
        job_id = task_info.job_id

        try:
            # Create temporary files for communication
            input_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            output_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)

            # Prepare task data
            task_data = {
                "goal": task_info.content,
                "device_serial": task_info.device,
                "job_id": job_id,
                "max_steps": 15,
                "timeout": 1000,
                "max_retries": 3,
                "debug": False
            }

            # Write task data to input file
            logger.info(f"Writing task data to {input_file.name}")
            logger.debug(f"Task data: {task_data}")
            json.dump(task_data, input_file)
            input_file.close()

            output_file.close()

            # Start subprocess
            cmd = [
                "python",
                "subprocess_worker.py",
                input_file.name,
                output_file.name
            ]

            logger.info(f"Starting subprocess for task {job_id} with command: {' '.join(cmd)}")
            logger.info(f"Input file: {input_file.name}")
            logger.info(f"Output file: {output_file.name}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd="."  # Ensure we're in the right directory
            )

            # Store process reference
            self.active_processes[job_id] = process
            logger.info(f"Subprocess started with PID: {process.pid}")

            # Wait for process to complete
            loop_count = 0
            while process.poll() is None:
                loop_count += 1
                if loop_count % 10 == 0:  # Log every 5 seconds
                    logger.info(f"Subprocess {job_id} (PID: {process.pid}) still running...")

                # Check if task was cancelled
                from task_manager import task_manager
                current_task = task_manager.get_task(job_id)
                if current_task and current_task.state.value == "cancelled":
                    logger.info(f"Terminating subprocess for cancelled task {job_id} (PID: {process.pid})")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                        logger.info(f"Subprocess {job_id} terminated gracefully")
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Force killing subprocess for task {job_id} (PID: {process.pid})")
                        process.kill()
                        process.wait()
                        logger.info(f"Subprocess {job_id} force killed")

                    # Clean up
                    self.active_processes.pop(job_id, None)
                    Path(input_file.name).unlink(missing_ok=True)
                    Path(output_file.name).unlink(missing_ok=True)

                    raise asyncio.CancelledError("Task was cancelled")

                # Sleep briefly to avoid busy waiting
                await asyncio.sleep(0.5)

            # Process completed, read result
            return_code = process.returncode
            stdout, stderr = process.communicate()

            logger.info(f"Subprocess {job_id} completed with return code: {return_code}")
            if stdout:
                logger.info(f"Subprocess {job_id} stdout: {stdout}")
            if stderr:
                logger.warning(f"Subprocess {job_id} stderr: {stderr}")

            # Clean up process reference
            self.active_processes.pop(job_id, None)

            if return_code == 0:
                # Read result from output file
                try:
                    logger.info(f"Reading result from output file: {output_file.name}")
                    with open(output_file.name, 'r') as f:
                        result = json.load(f)
                    logger.info(f"Subprocess task {job_id} completed successfully")
                    logger.debug(f"Task result: {result}")
                    return result
                except Exception as e:
                    logger.error(f"Failed to read result for task {job_id}: {e}")
                    logger.error(f"Output file exists: {Path(output_file.name).exists()}")
                    if Path(output_file.name).exists():
                        try:
                            with open(output_file.name, 'r') as f:
                                content = f.read()
                            logger.error(f"Output file content: {content}")
                        except:
                            logger.error("Could not read output file content")
                    return {
                        "success": False,
                        "reason": f"Failed to read result: {str(e)}"
                    }
            else:
                # Process failed
                logger.error(f"Subprocess task {job_id} failed with code {return_code}")
                if stderr:
                    logger.error(f"Stderr: {stderr}")
                if stdout:
                    logger.error(f"Stdout: {stdout}")
                return {
                    "success": False,
                    "reason": f"Subprocess failed with code {return_code}: {stderr}"
                }

        except asyncio.CancelledError:
            # Handle cancellation
            if job_id in self.active_processes:
                process = self.active_processes[job_id]
                logger.info(f"Cancelling subprocess for task {job_id}")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing subprocess for task {job_id}")
                    process.kill()
                    process.wait()
                self.active_processes.pop(job_id, None)
            raise

        except Exception as e:
            logger.error(f"Error executing subprocess task {job_id}: {e}")
            # Clean up process if it exists
            if job_id in self.active_processes:
                process = self.active_processes[job_id]
                process.terminate()
                self.active_processes.pop(job_id, None)
            return {
                "success": False,
                "reason": f"Subprocess execution error: {str(e)}"
            }

        finally:
            # Clean up temporary files
            try:
                Path(input_file.name).unlink(missing_ok=True)
                Path(output_file.name).unlink(missing_ok=True)
            except:
                pass

    def cancel_task(self, job_id: str) -> bool:
        """Cancel a running task by terminating its subprocess."""
        if job_id in self.active_processes:
            process = self.active_processes[job_id]
            logger.info(f"Terminating subprocess for task {job_id}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning(f"Force killing subprocess for task {job_id}")
                process.kill()
                process.wait()

            self.active_processes.pop(job_id, None)
            return True
        return False

    def get_active_tasks(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active subprocess tasks."""
        active_info = {}
        for job_id, process in self.active_processes.items():
            active_info[job_id] = {
                "pid": process.pid,
                "running": process.poll() is None,
                "return_code": process.returncode
            }
        return active_info

    def cleanup(self):
        """Clean up all active processes."""
        for job_id, process in list(self.active_processes.items()):
            logger.info(f"Cleaning up subprocess for task {job_id}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        self.active_processes.clear()


# Global subprocess executor instance
subprocess_executor = SubprocessTaskExecutor()
