"""
Test script to verify the uvloop fix.
"""

import asyncio
import httpx
import time


BASE_URL = "http://localhost:43826"


async def test_task_execution():
    """Test that tasks can execute without uvloop errors."""
    print("Testing task execution with uvloop fix...")
    
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "test_app",
            "nonce": "test_nonce"
        },
        "arg": {
            "site": "test-site",
            "eacId": "test-eac",
            "jobId": f"job-fix-test-{int(time.time())}",
            "eventType": "Test",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Submit task
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request
        )
        
        print(f"Submit response: {response.status_code}")
        result = response.json()
        print(f"Submit result: {result}")
        
        if response.status_code != 200 or result.get("code") != 0:
            print("❌ Task submission failed")
            return None
            
        job_id = test_request["arg"]["jobId"]
        print(f"✅ Task submitted: {job_id}")
        
        # Monitor task for a while
        for i in range(10):  # Check for 10 seconds
            await asyncio.sleep(1)
            
            status_response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get("code") == 0:
                    data = status_result.get("data", {})
                    state = data.get("state")
                    msg = data.get("resultMsg", "")
                    print(f"Task {job_id}: {state} - {msg}")
                    
                    if state in ["completed", "failed"]:
                        if state == "completed":
                            print("✅ Task completed successfully!")
                        else:
                            print(f"❌ Task failed: {msg}")
                        return job_id
                    elif state == "running":
                        print("✅ Task is running (no uvloop error!)")
                        # Cancel the task to test cancellation
                        cancel_response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
                        if cancel_response.status_code == 200:
                            cancel_result = cancel_response.json()
                            if cancel_result.get("code") == 0:
                                print("✅ Task cancellation requested")
                                # Wait a bit more to see if it gets cancelled
                                await asyncio.sleep(3)
                                final_status = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
                                if final_status.status_code == 200:
                                    final_result = final_status.json()
                                    if final_result.get("code") == 0:
                                        final_data = final_result.get("data", {})
                                        final_state = final_data.get("state")
                                        print(f"Final task state: {final_state}")
                                        if final_state == "cancelled":
                                            print("✅ Task successfully cancelled!")
                                        else:
                                            print(f"⚠️  Task state is {final_state}, not cancelled")
                                return job_id
        
        print("⚠️  Task monitoring timed out")
        return job_id


async def main():
    """Main test function."""
    print("🧪 Testing uvloop fix...")
    
    # Check health
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Service is healthy")
            else:
                print("❌ Service health check failed")
                return
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return
    
    await test_task_execution()
    print("\n🎉 Test completed!")


if __name__ == "__main__":
    asyncio.run(main())
