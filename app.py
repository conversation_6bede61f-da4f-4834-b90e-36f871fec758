"""
FastAPI application for droidrun HTTP service.
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from llama_index.llms.deepseek import DeepSeek

from droidrun.agent.droid import Dr<PERSON><PERSON><PERSON>
from models import (
    RunTaskRequest, StandardResponse, TaskStatusResponse, 
    TaskState, TaskInfo
)
from task_manager import task_manager
from config import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting FastAPI droidrun service...")
    await task_manager.start()
    yield
    # Shutdown
    logger.info("Shutting down FastAPI droidrun service...")
    await task_manager.stop()


# Create FastAPI app
app = FastAPI(
    title="DroidRun HTTP Service",
    description="FastAPI service for executing Android automation tasks via droidrun",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def create_llm():
    """Create and configure the LLM instance."""
    return DeepSeek(
        model=config.LLM_MODEL,
        api_key=config.LLM_API_KEY,
        api_base=config.LLM_API_BASE,
        temperature=config.LLM_TEMPERATURE
    )


async def execute_droid_task(task_info: TaskInfo) -> Dict[str, Any]:
    """Execute a droidrun task."""
    logger.info(f"Executing droidrun task {task_info.job_id}: {task_info.content}")
    
    try:
        # Create LLM instance
        llm = create_llm()
        
        # Create and configure DroidAgent
        agent = DroidAgent(
            goal=task_info.content,
            llm=llm,
            max_steps=config.MAX_STEPS,
            device_serial=task_info.device,
            timeout=config.TIMEOUT,
            max_retries=config.MAX_RETRIES,
            debug=config.DEBUG
        )
        
        # Execute the agent
        result = await agent.run()
        
        logger.info(f"Task {task_info.job_id} completed with success: {result.get('success', False)}")
        return result
        
    except Exception as e:
        logger.error(f"Error executing task {task_info.job_id}: {e}")
        return {
            "success": False,
            "reason": f"Execution error: {str(e)}"
        }


@app.post("/api/aia/run-task", response_model=StandardResponse)
async def run_task(request: RunTaskRequest) -> StandardResponse:
    """
    Execute a droidrun task asynchronously.
    
    This endpoint accepts a task request, creates the task, starts it in background,
    and immediately returns a response. The actual task execution happens asynchronously.
    """
    try:
        job_id = request.arg.jobId
        
        # Check if task already exists
        existing_task = task_manager.get_task(job_id)
        if existing_task:
            if existing_task.state in [TaskState.PENDING, TaskState.RUNNING]:
                return StandardResponse(
                    code=1,
                    msg=f"Task {job_id} is already running",
                    data={}
                )
            else:
                # Task exists but completed/failed, allow recreation
                logger.info(f"Recreating completed task {job_id}")
        
        # Create new task
        task_info = task_manager.create_task(
            job_id=job_id,
            eac_id=request.arg.eacId,
            event_type=request.arg.eventType,
            content=request.arg.content,
            device=request.arg.device,
            auth_info=request.auth,
            callback_url=None  # TODO: Add callback URL to request if needed
        )
        
        # Start task execution in background
        success = task_manager.start_task(job_id, execute_droid_task)
        
        if not success:
            return StandardResponse(
                code=1,
                msg=f"Failed to start task {job_id}",
                data={}
            )
        
        logger.info(f"Task {job_id} started successfully")
        return StandardResponse(
            code=0,
            msg="",
            data={}
        )
        
    except Exception as e:
        logger.error(f"Error in run_task: {e}")
        return StandardResponse(
            code=1,
            msg=f"Internal error: {str(e)}",
            data={}
        )


@app.get("/api/aia/task-status/{job_id}", response_model=TaskStatusResponse)
async def get_task_status(job_id: str) -> TaskStatusResponse:
    """Get the status of a task."""
    try:
        task_info = task_manager.get_task(job_id)
        
        if not task_info:
            return TaskStatusResponse(
                code=1,
                msg=f"Task {job_id} not found",
                data={}
            )
        
        # Prepare status data
        status_data = {
            "jobId": task_info.job_id,
            "eacId": task_info.eac_id,
            "eventType": task_info.event_type,
            "state": task_info.state.value,
            "content": task_info.content,
            "device": task_info.device,
            "createdAt": task_info.created_at,
            "startedAt": task_info.started_at,
            "completedAt": task_info.completed_at,
            "resultMsg": task_info.result_msg
        }
        
        return TaskStatusResponse(
            code=0,
            msg="",
            data=status_data
        )
        
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        return TaskStatusResponse(
            code=1,
            msg=f"Internal error: {str(e)}",
            data={}
        )


@app.post("/api/aia/stop-task/{job_id}", response_model=StandardResponse)
async def stop_task(job_id: str) -> StandardResponse:
    """Stop/cancel a running task."""
    try:
        task_info = task_manager.get_task(job_id)
        
        if not task_info:
            return StandardResponse(
                code=1,
                msg=f"Task {job_id} not found",
                data={}
            )
        
        if task_info.state not in [TaskState.PENDING, TaskState.RUNNING]:
            return StandardResponse(
                code=1,
                msg=f"Task {job_id} is not running (state: {task_info.state.value})",
                data={}
            )
        
        # Cancel the task
        success = task_manager.cancel_task(job_id)
        
        if success:
            logger.info(f"Task {job_id} cancelled successfully")
            return StandardResponse(
                code=0,
                msg="Task cancelled successfully",
                data={}
            )
        else:
            return StandardResponse(
                code=1,
                msg=f"Failed to cancel task {job_id}",
                data={}
            )
        
    except Exception as e:
        logger.error(f"Error stopping task: {e}")
        return StandardResponse(
            code=1,
            msg=f"Internal error: {str(e)}",
            data={}
        )


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": time.time()}


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "DroidRun HTTP Service",
        "version": "1.0.0",
        "status": "running"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG
    )
