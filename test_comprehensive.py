"""
Comprehensive test script to verify all fixes.
"""

import asyncio
import httpx
import time


BASE_URL = "http://localhost:43826"


async def check_service_health():
    """Check if service is healthy."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Service is healthy")
                return True
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Cannot connect to service: {e}")
        return False


async def submit_task(task_name: str):
    """Submit a task and return job_id."""
    print(f"Submitting task: {task_name}")
    
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "test_app",
            "nonce": "test_nonce"
        },
        "arg": {
            "site": "test-site",
            "eacId": "test-eac",
            "jobId": f"job-{task_name}-{int(time.time())}",
            "eventType": "Test",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                job_id = test_request["arg"]["jobId"]
                print(f"✅ Task submitted: {job_id}")
                return job_id
            else:
                print(f"❌ Task submission failed: {result.get('msg')}")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None


async def get_task_status(job_id: str):
    """Get task status."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                data = result.get("data", {})
                state = data.get("state")
                msg = data.get("resultMsg", "")
                return state, msg
        return None, None


async def cancel_task(job_id: str):
    """Cancel a task."""
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
        
        if response.status_code == 200:
            result = response.json()
            return result.get("code") == 0, result.get("msg", "")
        return False, f"HTTP error: {response.status_code}"


async def get_active_agents():
    """Get active agents info."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/debug/active-agents")
        
        if response.status_code == 200:
            return response.json()
        return None


async def test_task_execution_and_cancellation():
    """Test task execution and cancellation."""
    print("\n" + "="*60)
    print("TEST: Task Execution and Cancellation")
    print("="*60)
    
    # Submit task
    job_id = await submit_task("cancel-test")
    if not job_id:
        return False
    
    # Wait for task to start
    print("Waiting for task to start...")
    for i in range(10):
        await asyncio.sleep(1)
        state, msg = await get_task_status(job_id)
        print(f"Task state: {state} - {msg}")
        
        if state == "running":
            print("✅ Task is running!")
            break
        elif state in ["completed", "failed"]:
            print(f"Task finished too quickly: {state}")
            return False
    else:
        print("❌ Task did not start running")
        return False
    
    # Check active agents
    active_info = await get_active_agents()
    if active_info:
        print(f"Active agents: {active_info['active_agents_count']}")
        if job_id in active_info['active_agents']:
            print("✅ Task found in active agents registry")
        else:
            print("❌ Task not found in active agents registry")
    
    # Cancel the task
    print("Cancelling task...")
    cancel_success, cancel_msg = await cancel_task(job_id)
    
    if cancel_success:
        print(f"✅ Cancel request accepted: {cancel_msg}")
        
        # Wait for cancellation to take effect
        print("Waiting for cancellation to take effect...")
        for i in range(10):
            await asyncio.sleep(1)
            state, msg = await get_task_status(job_id)
            print(f"Task state: {state} - {msg}")
            
            if state == "cancelled":
                print("✅ Task successfully cancelled!")
                
                # Check that it's removed from active agents
                active_info = await get_active_agents()
                if active_info and job_id not in active_info['active_agents']:
                    print("✅ Task removed from active agents registry")
                else:
                    print("❌ Task still in active agents registry")
                
                return True
            elif state in ["completed", "failed"]:
                print(f"❌ Task finished with {state} instead of being cancelled")
                return False
        
        print("❌ Task was not cancelled within timeout")
        return False
    else:
        print(f"❌ Cancel request failed: {cancel_msg}")
        return False


async def test_uvloop_compatibility():
    """Test that uvloop compatibility issues are resolved."""
    print("\n" + "="*60)
    print("TEST: uvloop Compatibility")
    print("="*60)
    
    # Submit a simple task
    job_id = await submit_task("uvloop-test")
    if not job_id:
        return False
    
    # Monitor for uvloop errors
    print("Monitoring for uvloop errors...")
    for i in range(15):  # Monitor for 15 seconds
        await asyncio.sleep(1)
        state, msg = await get_task_status(job_id)
        
        if state == "failed" and "uvloop" in (msg or "").lower():
            print(f"❌ uvloop error detected: {msg}")
            return False
        elif state == "running":
            print("✅ Task running without uvloop errors")
            # Cancel to clean up
            await cancel_task(job_id)
            return True
        elif state in ["completed", "cancelled"]:
            print(f"✅ Task finished normally: {state}")
            return True
    
    print("⚠️  Task monitoring timed out")
    # Clean up
    await cancel_task(job_id)
    return True


async def main():
    """Main test function."""
    print("🧪 Starting Comprehensive Tests...")
    
    # Check service health
    if not await check_service_health():
        return
    
    # Run tests
    test1_result = await test_uvloop_compatibility()
    test2_result = await test_task_execution_and_cancellation()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"uvloop Compatibility: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Task Execution & Cancellation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests PASSED!")
        print("✅ uvloop compatibility issues resolved")
        print("✅ Task cancellation working properly")
    else:
        print("\n❌ Some tests FAILED!")


if __name__ == "__main__":
    asyncio.run(main())
