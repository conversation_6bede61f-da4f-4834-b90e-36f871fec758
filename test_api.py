"""
Test script for the FastAPI droidrun service.
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any


BASE_URL = "http://localhost:43826"


async def test_health_check():
    """Test health check endpoint."""
    print("Testing health check...")
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/health")
        print(f"Health check response: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200


async def test_root_endpoint():
    """Test root endpoint."""
    print("\nTesting root endpoint...")
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/")
        print(f"Root response: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200


async def test_run_task():
    """Test run task endpoint."""
    print("\nTesting run task endpoint...")
    
    # Prepare test request
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "wbb2dej3",
            "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
        },
        "arg": {
            "site": "eac-android-any",
            "eacId": "eac-0112",
            "jobId": f"job-test-{int(time.time())}",
            "eventType": "Autonomous",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{BASE_URL}/api/aia/run-task",
            json=test_request,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Run task response: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("✅ Task submitted successfully!")
                return test_request["arg"]["jobId"]
            else:
                print(f"❌ Task submission failed: {result.get('msg')}")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None


async def test_task_status(job_id: str):
    """Test task status endpoint."""
    print(f"\nTesting task status for job {job_id}...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/api/aia/task-status/{job_id}")
        print(f"Task status response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get("code") == 0:
                task_data = result.get("data", {})
                state = task_data.get("state")
                print(f"Task state: {state}")
                return state
            else:
                print(f"❌ Error getting task status: {result.get('msg')}")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None


async def test_stop_task(job_id: str):
    """Test stop task endpoint."""
    print(f"\nTesting stop task for job {job_id}...")
    
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{BASE_URL}/api/aia/stop-task/{job_id}")
        print(f"Stop task response: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("✅ Task stopped successfully!")
                return True
            else:
                print(f"❌ Failed to stop task: {result.get('msg')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False


async def monitor_task_execution(job_id: str, max_wait_time: int = 120):
    """Monitor task execution until completion."""
    print(f"\nMonitoring task {job_id} execution...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        state = await test_task_status(job_id)
        
        if state in ["completed", "failed", "cancelled"]:
            print(f"Task {job_id} finished with state: {state}")
            return state
        elif state in ["pending", "running"]:
            print(f"Task {job_id} is still {state}, waiting...")
            await asyncio.sleep(5)
        else:
            print(f"Unknown task state: {state}")
            break
    
    print(f"Task monitoring timed out after {max_wait_time} seconds")
    return None


async def main():
    """Main test function."""
    print("🚀 Starting FastAPI droidrun service tests...")
    
    # Test basic endpoints
    health_ok = await test_health_check()
    root_ok = await test_root_endpoint()
    
    if not (health_ok and root_ok):
        print("❌ Basic endpoint tests failed!")
        return
    
    print("✅ Basic endpoints working!")
    
    # Test task submission
    job_id = await test_run_task()
    if not job_id:
        print("❌ Task submission failed!")
        return
    
    # Monitor task execution for a short time
    print(f"\nWaiting 10 seconds to let task start...")
    await asyncio.sleep(10)
    
    # Check task status
    state = await test_task_status(job_id)
    
    if state in ["pending", "running"]:
        print(f"Task is {state}, testing stop functionality...")
        # Test stop task
        stop_ok = await test_stop_task(job_id)
        if stop_ok:
            # Check final state
            await asyncio.sleep(2)
            final_state = await test_task_status(job_id)
            print(f"Final task state: {final_state}")
    elif state in ["completed", "failed"]:
        print(f"Task already finished with state: {state}")
    
    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
