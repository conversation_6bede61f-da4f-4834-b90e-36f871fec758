"""
Configuration settings for the FastAPI droidrun service.
"""

import os
from typing import Optional


class Config:
    """Application configuration."""
    
    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 43827
    
    # LLM settings
    LLM_PROVIDER: str = os.getenv("LLM_PROVIDER", "DeepSeek")
    LLM_MODEL: str = os.getenv("LLM_MODEL", "deepseek-v3-250324")
    LLM_API_KEY: str = os.getenv("LLM_API_KEY", "1444135e-36b0-4f40-9130-7524530ea32c")
    LLM_API_BASE: str = os.getenv("LLM_API_BASE", "https://ark.cn-beijing.volces.com/api/v3")
    LLM_TEMPERATURE: float = float(os.getenv("LLM_TEMPERATURE", "0.2"))
    
    # Task execution settings
    MAX_STEPS: int = int(os.getenv("MAX_STEPS", "15"))
    TIMEOUT: int = int(os.getenv("TIMEOUT", "1000"))
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "3"))
    
    # Default device serial (can be overridden by request)
    DEFAULT_DEVICE_SERIAL: Optional[str] = os.getenv("DEFAULT_DEVICE_SERIAL")
    
    # Callback settings
    CALLBACK_TIMEOUT: int = int(os.getenv("CALLBACK_TIMEOUT", "30"))
    CALLBACK_RETRIES: int = int(os.getenv("CALLBACK_RETRIES", "3"))
    
    # Task cleanup settings
    TASK_CLEANUP_INTERVAL: int = int(os.getenv("TASK_CLEANUP_INTERVAL", "3600"))  # 1 hour
    TASK_MAX_AGE: int = int(os.getenv("TASK_MAX_AGE", "86400"))  # 24 hours
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Debug mode
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"


# Global config instance
config = Config()
