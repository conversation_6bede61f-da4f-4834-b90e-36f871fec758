"""
Subprocess worker for executing DroidRun tasks.
This runs in a separate process to avoid LlamaIndex instrumentation issues.
"""

import asyncio
import json
import logging
import os
import sys
import traceback
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("droidrun-worker")


async def execute_droid_task(task_data: dict) -> dict:
    """Execute a DroidRun task in this subprocess."""
    job_id = task_data.get("job_id", "unknown")

    try:
        logger.info(f"Worker starting task execution for job {job_id}")
        logger.info(f"Task goal: {task_data['goal']}")
        logger.info(f"Device serial: {task_data.get('device_serial', 'default')}")
        logger.info(f"Max steps: {task_data.get('max_steps', 15)}")

        # Import here to avoid issues with the main process
        logger.info("Importing DroidRun components...")
        from llama_index.llms.deepseek import DeepSeek
        from droidrun.agent.droid import DroidAgent
        from config import config

        logger.info("Creating LLM instance...")
        # Create LLM instance
        llm = DeepSeek(
            model=config.LLM_MODEL,
            api_key=config.LLM_API_KEY,
            api_base=config.LLM_API_BASE,
            temperature=config.LLM_TEMPERATURE
        )
        logger.info(f"LLM created: {config.LLM_MODEL}")

        logger.info("Creating DroidAgent...")
        # Create DroidAgent
        agent = DroidAgent(
            goal=task_data["goal"],
            llm=llm,
            max_steps=task_data.get("max_steps", 15),
            device_serial=task_data.get("device_serial"),
            timeout=task_data.get("timeout", 1000),
            max_retries=task_data.get("max_retries", 3),
            debug=task_data.get("debug", False),
            enable_tracing=False  # Disable tracing in subprocess
        )
        logger.info("DroidAgent created successfully")

        # Execute the agent
        logger.info("Starting DroidAgent execution...")
        result = await agent.run()

        logger.info(f"DroidAgent execution completed")
        logger.info(f"Task success: {result.get('success', False)}")
        logger.info(f"Task reason: {result.get('reason', 'No reason provided')}")

        return result

    except Exception as e:
        logger.error(f"Error in subprocess worker for job {job_id}: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")
        return {
            "success": False,
            "reason": f"Worker error: {str(e)}"
        }


def main():
    """Main function for subprocess worker."""
    logger.info(f"Subprocess worker started with PID: {os.getpid()}")
    logger.info(f"Command line args: {sys.argv}")

    if len(sys.argv) != 3:
        logger.error("Invalid arguments. Usage: subprocess_worker.py <input_file> <output_file>")
        print("Usage: subprocess_worker.py <input_file> <output_file>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    logger.info(f"Input file: {input_file}")
    logger.info(f"Output file: {output_file}")

    try:
        # Read task data
        logger.info("Reading task data from input file...")
        with open(input_file, 'r') as f:
            task_data = json.load(f)

        job_id = task_data.get('job_id', 'unknown')
        logger.info(f"Worker started for task: {job_id}")
        logger.info(f"Task data loaded: {task_data}")

        # Execute task
        logger.info("Starting task execution...")
        result = asyncio.run(execute_droid_task(task_data))

        # Write result
        logger.info(f"Writing result to output file: {output_file}")
        logger.info(f"Result: {result}")
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)

        logger.info(f"Worker completed successfully for task {job_id}")
        sys.exit(0)

    except Exception as e:
        logger.error(f"Worker failed: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")

        # Write error result
        try:
            error_result = {
                "success": False,
                "reason": f"Worker failed: {str(e)}"
            }
            logger.info(f"Writing error result to output file: {output_file}")
            with open(output_file, 'w') as f:
                json.dump(error_result, f, indent=2)
        except Exception as write_error:
            logger.error(f"Failed to write error result: {write_error}")

        sys.exit(1)


if __name__ == "__main__":
    main()
