"""
Subprocess worker for executing DroidRun tasks.
This runs in a separate process to avoid LlamaIndex instrumentation issues.
"""

import asyncio
import json
import logging
import sys
import traceback
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("droidrun-worker")


async def execute_droid_task(task_data: dict) -> dict:
    """Execute a DroidRun task in this subprocess."""
    try:
        # Import here to avoid issues with the main process
        from llama_index.llms.deepseek import DeepSeek
        from droidrun.agent.droid import DroidAgent
        from config import config
        
        logger.info(f"Worker executing task: {task_data['goal']}")
        
        # Create LLM instance
        llm = DeepSeek(
            model=config.LLM_MODEL,
            api_key=config.LLM_API_KEY,
            api_base=config.LLM_API_BASE,
            temperature=config.LLM_TEMPERATURE
        )
        
        # Create DroidAgent
        agent = DroidAgent(
            goal=task_data["goal"],
            llm=llm,
            max_steps=task_data.get("max_steps", 15),
            device_serial=task_data.get("device_serial"),
            timeout=task_data.get("timeout", 1000),
            max_retries=task_data.get("max_retries", 3),
            debug=task_data.get("debug", False),
            enable_tracing=False  # Disable tracing in subprocess
        )
        
        # Execute the agent
        logger.info("Starting DroidAgent execution...")
        result = await agent.run()
        
        logger.info(f"Task completed with success: {result.get('success', False)}")
        return result
        
    except Exception as e:
        logger.error(f"Error in subprocess worker: {e}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "reason": f"Worker error: {str(e)}"
        }


def main():
    """Main function for subprocess worker."""
    if len(sys.argv) != 3:
        print("Usage: subprocess_worker.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        # Read task data
        with open(input_file, 'r') as f:
            task_data = json.load(f)
        
        logger.info(f"Worker started for task: {task_data.get('job_id', 'unknown')}")
        
        # Execute task
        result = asyncio.run(execute_droid_task(task_data))
        
        # Write result
        with open(output_file, 'w') as f:
            json.dump(result, f)
        
        logger.info("Worker completed successfully")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"Worker failed: {e}")
        logger.error(traceback.format_exc())
        
        # Write error result
        try:
            error_result = {
                "success": False,
                "reason": f"Worker failed: {str(e)}"
            }
            with open(output_file, 'w') as f:
                json.dump(error_result, f)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
