"""
Pydantic models for the FastAPI droidrun service.
"""

from typing import Optional, Any, Dict
from pydantic import BaseModel, Field
from enum import Enum


class TaskState(str, Enum):
    """Task execution states."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AuthModel(BaseModel):
    """Authentication model."""
    appId: str = Field(..., description="Application ID")
    nonce: str = Field(..., description="Random nonce for request")


class TaskArgModel(BaseModel):
    """Task arguments model."""
    site: str = Field(..., description="Site identifier")
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Job identifier")
    eventType: str = Field(..., description="Event type")
    content: str = Field(..., description="Task goal/content")
    device: str = Field(..., description="Device serial number")


class RunTaskRequest(BaseModel):
    """Request model for /api/aia/run-task endpoint."""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication info")
    arg: TaskArgModel = Field(..., description="Task arguments")


class StandardResponse(BaseModel):
    """Standard response format."""
    code: int = Field(0, description="Response code (0=success)")
    msg: str = Field("", description="Response message")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")


class TaskStatusResponse(BaseModel):
    """Task status response."""
    code: int = Field(0, description="Response code")
    msg: str = Field("", description="Response message")
    data: Dict[str, Any] = Field(default_factory=dict, description="Task status data")


class CallbackArgModel(BaseModel):
    """Callback arguments model."""
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Job identifier")
    eventType: str = Field(..., description="Event type")
    state: int = Field(..., description="Task state (1=success, 3=failure)")
    msg: str = Field(..., description="Result message")


class CallbackRequest(BaseModel):
    """Callback request model for reporting results."""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication info")
    arg: CallbackArgModel = Field(..., description="Callback arguments")


class CallbackResponse(BaseModel):
    """Callback response model."""
    errorCode: int = Field(0, description="Error code")
    errorMessage: str = Field("succ", description="Error message")


class TaskInfo(BaseModel):
    """Internal task information model."""
    job_id: str
    eac_id: str
    event_type: str
    content: str
    device: str
    state: TaskState
    result_msg: Optional[str] = None
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    callback_url: Optional[str] = None
    auth_info: Optional[AuthModel] = None
    task_handle: Optional[Any] = None  # asyncio.Task handle
